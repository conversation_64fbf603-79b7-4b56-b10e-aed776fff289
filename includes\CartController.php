<?php
/**
 * Pretty Urls Module
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2025
 * @license   Academic Free License (AFL 3.0)
 * @version   1.0.0
 */


class CartController extends CartControllerCore
{
    public function displayAjaxProductRefresh()
    {
        if ($this->id_product) {
            $url = $this->context->link->getProductLink(
                $this->id_product,
                null,
                null,
                null,
                $this->context->language->id,
                null,
                (int)Product::getIdProductAttributesByIdAttributes($this->id_product, Tools::getValue('group'), true),
                false,
                false,
                true,
                ['quantity_wanted' => (int)$this->qty]
            );
       $id_unique_ipa = (int)Product::getIdProductAttributesByIdAttributes($this->id_product, Tools::getValue('group'), true);
        $this->context->cookie->id_unique_ipa = $id_unique_ipa;
        $this->context->cookie->write();
        } else {
            $url = false;
        }
        ob_end_clean();
        header('Content-Type: application/json');
        $this->ajaxDie(Tools::jsonEncode([
            'success' => true,
            'productUrl' => $url
        ]));
    }
}
