
<?php
/**
 * Pretty URLs Pro - Lightweight SEO Module
 *
 * <AUTHOR>
 * @copyright Copyright (c) 2025
 * @license   Academic Free License (AFL 3.0)
 * @version   2.0.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class Prettyurls extends Module
{
    private $config_keys = [
        'PRETTYURLS_ENABLE_PRODUCTS' => 1,
        'PRETTYURLS_ENABLE_CATEGORIES' => 1,
        'PRETTYURLS_ENABLE_MANUFACTURERS' => 1,
        'PRETTYURLS_ENABLE_SUPPLIERS' => 1,
        'PRETTYURLS_ENABLE_CMS' => 1,
        'PRETTYURLS_REDIRECT_OLD' => 1,
        'PRETTYURLS_CACHE_TTL' => 3600,
        'PRETTYURLS_DEBUG_MODE' => 0,
        'PRETTYURLS_FORCE_LOWERCASE' => 1,
        'PRETTYURLS_REMOVE_ACCENTS' => 0,
    ];

    public function __construct()
    {
        $this->name = 'prettyurls';
        $this->tab = 'seo';
        $this->version = '2.0.0';
        $this->author = 'Sathi';
        $this->bootstrap = true;
        $this->need_instance = 0;
        $this->ps_versions_compliancy = ['min' => '*******', 'max' => '9.0.99'];

        parent::__construct();

        $this->displayName = $this->l('Pretty URLs Pro');
        $this->description = $this->l('Lightweight SEO module that removes IDs from URLs. Compatible with PrestaShop 1.7-9.0');
        $this->confirmUninstall = $this->l('Are you sure you want to uninstall Pretty URLs?');
    }

    public function install()
    {
        return parent::install()
            && $this->installConfiguration()
            && $this->installOverrides()
            && $this->registerHook('actionDispatcherBefore')
            && $this->registerHook('actionFrontControllerSetMedia')
            && $this->registerHook('displayBackOfficeHeader')
            && $this->registerHook('actionObjectProductUpdateAfter')
            && $this->registerHook('actionObjectCategoryUpdateAfter')
            && $this->registerHook('actionObjectManufacturerUpdateAfter')
            && $this->registerHook('actionObjectSupplierUpdateAfter')
            && $this->registerHook('actionObjectCmsUpdateAfter');
    }

    public function uninstall()
    {
        return $this->uninstallConfiguration()
            && $this->uninstallOverrides()
            && parent::uninstall();
    }

    private function installConfiguration()
    {
        foreach ($this->config_keys as $key => $value) {
            if (!Configuration::updateValue($key, $value)) {
                return false;
            }
        }
        return true;
    }

    private function uninstallConfiguration()
    {
        foreach (array_keys($this->config_keys) as $key) {
            Configuration::deleteByName($key);
        }
        return true;
    }

    public function installOverrides()
    {
        try {
            $overrides = [
                'classes/Link.php',
                'classes/Dispatcher.php'
            ];

            foreach ($overrides as $override) {
                $source = $this->getLocalPath() . 'override/' . $override;
                $destination = _PS_OVERRIDE_DIR_ . $override;

                if (file_exists($source)) {
                    if (!$this->copyFile($source, $destination)) {
                        return false;
                    }
                }
            }

            return $this->clearCache();
        } catch (Exception $e) {
            return false;
        }
    }

    public function uninstallOverrides()
    {
        try {
            $overrides = [
                'classes/Link.php',
                'classes/Dispatcher.php'
            ];

            foreach ($overrides as $override) {
                $file = _PS_OVERRIDE_DIR_ . $override;
                if (file_exists($file)) {
                    unlink($file);
                }
            }

            return $this->clearCache();
        } catch (Exception $e) {
            return false;
        }
    }

    private function copyFile($source, $destination)
    {
        $dir = dirname($destination);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        return copy($source, $destination);
    }


    private function clearCache()
    {
        if (method_exists('Tools', 'clearSmartyCache')) {
            Tools::clearSmartyCache();
        }

        if (method_exists('Tools', 'clearXMLCache')) {
            Tools::clearXMLCache();
        }

        if (version_compare(_PS_VERSION_, '*******', '>=')) {
            if (method_exists('Tools', 'clearSf2Cache')) {
                Tools::clearSf2Cache();
            }
        }

        return true;
    }

    public function getContent()
    {
        $output = '';

        if (Tools::isSubmit('submitPrettyUrlsConfig')) {
            $output .= $this->processForm();
        }

        $output .= $this->displayForm();
        $output .= $this->displayInfo();

        return $output;
    }

    private function processForm()
    {
        $errors = [];

        foreach (array_keys($this->config_keys) as $key) {
            $value = Tools::getValue($key);

            // Validate numeric values
            if (in_array($key, ['PRETTYURLS_CACHE_TTL'])) {
                $value = (int)$value;
                if ($value < 0) $value = 3600;
            } else {
                $value = (bool)$value;
            }

            if (!Configuration::updateValue($key, $value)) {
                $errors[] = $this->l('Failed to save: ') . $key;
            }
        }

        if (empty($errors)) {
            $this->clearCache();
            return $this->displayConfirmation($this->l('Settings saved successfully!'));
        } else {
            return $this->displayError(implode('<br>', $errors));
        }
    }

    // Hook implementations
    public function hookActionDispatcherBefore($params)
    {
        // Clear URL cache when needed
        if (Configuration::get('PRETTYURLS_DEBUG_MODE')) {
            require_once dirname(__FILE__) . '/includes/Helper.php';
            PrettyUrlsHelper::log('Dispatcher hook triggered', 'debug');
        }
        return true;
    }

    public function hookActionFrontControllerSetMedia($params)
    {
        // This hook can be used for additional front-end processing
        return true;
    }

    public function hookDisplayBackOfficeHeader($params)
    {
        // Add admin CSS if needed
        if (file_exists($this->getLocalPath() . 'views/css/admin.css')) {
            $this->context->controller->addCSS($this->_path . 'views/css/admin.css');
        }
        return true;
    }

    // Cache management hooks
    public function hookActionObjectProductUpdateAfter($params)
    {
        $this->clearUrlCache('product', $params['object']->id);
        return true;
    }

    public function hookActionObjectCategoryUpdateAfter($params)
    {
        $this->clearUrlCache('category', $params['object']->id);
        return true;
    }

    public function hookActionObjectManufacturerUpdateAfter($params)
    {
        $this->clearUrlCache('manufacturer', $params['object']->id);
        return true;
    }

    public function hookActionObjectSupplierUpdateAfter($params)
    {
        $this->clearUrlCache('supplier', $params['object']->id);
        return true;
    }

    public function hookActionObjectCmsUpdateAfter($params)
    {
        $this->clearUrlCache('cms', $params['object']->id);
        return true;
    }

    private function clearUrlCache($type, $id)
    {
        if (file_exists(dirname(__FILE__) . '/includes/Helper.php')) {
            require_once dirname(__FILE__) . '/includes/Helper.php';
            PrettyUrlsHelper::clearCache();

            if (Configuration::get('PRETTYURLS_DEBUG_MODE')) {
                PrettyUrlsHelper::log("Cleared cache for {$type} ID: {$id}", 'info');
            }
        }
    }

    private function displayForm()
    {
        $fields_form = [
            'form' => [
                'legend' => [
                    'title' => $this->l('Pretty URLs Configuration'),
                    'icon' => 'icon-cogs'
                ],
                'input' => [
                    [
                        'type' => 'switch',
                        'label' => $this->l('Enable for Products'),
                        'name' => 'PRETTYURLS_ENABLE_PRODUCTS',
                        'desc' => $this->l('Remove IDs from product URLs'),
                        'values' => [
                            ['id' => 'active_on', 'value' => 1, 'label' => $this->l('Yes')],
                            ['id' => 'active_off', 'value' => 0, 'label' => $this->l('No')]
                        ]
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Enable for Categories'),
                        'name' => 'PRETTYURLS_ENABLE_CATEGORIES',
                        'desc' => $this->l('Remove IDs from category URLs'),
                        'values' => [
                            ['id' => 'cat_on', 'value' => 1, 'label' => $this->l('Yes')],
                            ['id' => 'cat_off', 'value' => 0, 'label' => $this->l('No')]
                        ]
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Enable for Manufacturers'),
                        'name' => 'PRETTYURLS_ENABLE_MANUFACTURERS',
                        'desc' => $this->l('Remove IDs from manufacturer URLs'),
                        'values' => [
                            ['id' => 'man_on', 'value' => 1, 'label' => $this->l('Yes')],
                            ['id' => 'man_off', 'value' => 0, 'label' => $this->l('No')]
                        ]
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Enable for Suppliers'),
                        'name' => 'PRETTYURLS_ENABLE_SUPPLIERS',
                        'desc' => $this->l('Remove IDs from supplier URLs'),
                        'values' => [
                            ['id' => 'sup_on', 'value' => 1, 'label' => $this->l('Yes')],
                            ['id' => 'sup_off', 'value' => 0, 'label' => $this->l('No')]
                        ]
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Enable for CMS'),
                        'name' => 'PRETTYURLS_ENABLE_CMS',
                        'desc' => $this->l('Remove IDs from CMS page URLs'),
                        'values' => [
                            ['id' => 'cms_on', 'value' => 1, 'label' => $this->l('Yes')],
                            ['id' => 'cms_off', 'value' => 0, 'label' => $this->l('No')]
                        ]
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Redirect Old URLs'),
                        'name' => 'PRETTYURLS_REDIRECT_OLD',
                        'desc' => $this->l('Automatically redirect old URLs with IDs'),
                        'values' => [
                            ['id' => 'redirect_on', 'value' => 1, 'label' => $this->l('Yes')],
                            ['id' => 'redirect_off', 'value' => 0, 'label' => $this->l('No')]
                        ]
                    ],
                    [
                        'type' => 'text',
                        'label' => $this->l('Cache TTL (seconds)'),
                        'name' => 'PRETTYURLS_CACHE_TTL',
                        'desc' => $this->l('Cache time-to-live in seconds (default: 3600)'),
                        'class' => 'fixed-width-sm'
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Force Lowercase'),
                        'name' => 'PRETTYURLS_FORCE_LOWERCASE',
                        'desc' => $this->l('Convert all URLs to lowercase'),
                        'values' => [
                            ['id' => 'lower_on', 'value' => 1, 'label' => $this->l('Yes')],
                            ['id' => 'lower_off', 'value' => 0, 'label' => $this->l('No')]
                        ]
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Debug Mode'),
                        'name' => 'PRETTYURLS_DEBUG_MODE',
                        'desc' => $this->l('Enable debug logging (disable in production)'),
                        'values' => [
                            ['id' => 'debug_on', 'value' => 1, 'label' => $this->l('Yes')],
                            ['id' => 'debug_off', 'value' => 0, 'label' => $this->l('No')]
                        ]
                    ]
                ],
                'submit' => [
                    'title' => $this->l('Save Settings'),
                    'class' => 'btn btn-default pull-right'
                ]
            ]
        ];

        $helper = new HelperForm();
        $helper->show_toolbar = false;
        $helper->table = $this->table;
        $helper->module = $this;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG', 0);
        $helper->identifier = $this->identifier;
        $helper->submit_action = 'submitPrettyUrlsConfig';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminModules', false)
            . '&configure=' . $this->name . '&tab_module=' . $this->tab . '&module_name=' . $this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');

        foreach (array_keys($this->config_keys) as $key) {
            $helper->fields_value[$key] = Configuration::get($key, $this->config_keys[$key]);
        }

        return $helper->generateForm([$fields_form]);
    }

    private function displayInfo()
    {
        return '<div class="alert alert-info">
            <h4>' . $this->l('Pretty URLs Pro v2.0') . '</h4>
            <p>' . $this->l('This lightweight module removes IDs from your URLs for better SEO.') . '</p>
            <ul>
                <li>' . $this->l('✓ Compatible with PrestaShop 1.7 - 9.0') . '</li>
                <li>' . $this->l('✓ Lightweight and fast') . '</li>
                <li>' . $this->l('✓ Easy configuration') . '</li>
                <li>' . $this->l('✓ Automatic redirects') . '</li>
            </ul>
            <p><strong>' . $this->l('Note:') . '</strong> ' . $this->l('Clear cache after changing settings.') . '</p>
        </div>';
    }
}
