<?php
/**
 * Pretty URLs Pro - Simple Helper
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2025
 * @license   Academic Free License (AFL 3.0)
 * @version   2.0.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class PrettyUrlsHelper
{
    private static $cache = [];
    private static $cache_file = null;

    /**
     * Initialize cache file path
     */
    private static function initCache()
    {
        if (self::$cache_file === null) {
            self::$cache_file = _PS_CACHE_DIR_ . 'prettyurls_cache.json';
        }
    }

    /**
     * Get value from cache
     */
    public static function getFromCache($key)
    {
        self::initCache();
        
        if (isset(self::$cache[$key])) {
            return self::$cache[$key];
        }

        if (file_exists(self::$cache_file)) {
            $cache_data = json_decode(file_get_contents(self::$cache_file), true);
            if (isset($cache_data[$key])) {
                $ttl = Configuration::get('PRETTYURLS_CACHE_TTL', 3600);
                if (time() - $cache_data[$key]['time'] < $ttl) {
                    self::$cache[$key] = $cache_data[$key]['data'];
                    return $cache_data[$key]['data'];
                }
            }
        }

        return false;
    }

    /**
     * Store value in cache
     */
    public static function setCache($key, $value)
    {
        self::initCache();
        
        self::$cache[$key] = $value;
        
        $cache_data = [];
        if (file_exists(self::$cache_file)) {
            $cache_data = json_decode(file_get_contents(self::$cache_file), true) ?: [];
        }
        
        $cache_data[$key] = [
            'data' => $value,
            'time' => time()
        ];
        
        file_put_contents(self::$cache_file, json_encode($cache_data));
    }

    /**
     * Clear cache
     */
    public static function clearCache()
    {
        self::initCache();
        
        self::$cache = [];
        if (file_exists(self::$cache_file)) {
            unlink(self::$cache_file);
        }
    }

    /**
     * Sanitize URL rewrite
     */
    public static function sanitizeRewrite($rewrite)
    {
        if (Configuration::get('PRETTYURLS_FORCE_LOWERCASE')) {
            $rewrite = Tools::strtolower($rewrite);
        }
        
        // Remove special characters
        $rewrite = preg_replace('/[^a-zA-Z0-9\-_]/', '', $rewrite);
        
        return $rewrite;
    }

    /**
     * Check if rewrite exists
     */
    public static function rewriteExists($rewrite, $type, $id_lang, $exclude_id = null)
    {
        $cache_key = "rewrite_exists_{$type}_{$rewrite}_{$id_lang}_{$exclude_id}";
        
        $cached = self::getFromCache($cache_key);
        if ($cached !== false) {
            return $cached;
        }

        $table = '';
        $id_field = '';
        
        switch ($type) {
            case 'product':
                $table = _DB_PREFIX_ . 'product_lang';
                $id_field = 'id_product';
                break;
            case 'category':
                $table = _DB_PREFIX_ . 'category_lang';
                $id_field = 'id_category';
                break;
            case 'cms':
                $table = _DB_PREFIX_ . 'cms_lang';
                $id_field = 'id_cms';
                break;
            default:
                return false;
        }

        $sql = 'SELECT ' . $id_field . ' FROM ' . $table . ' 
                WHERE link_rewrite = "' . pSQL($rewrite) . '" 
                AND id_lang = ' . (int)$id_lang;
        
        if ($exclude_id) {
            $sql .= ' AND ' . $id_field . ' != ' . (int)$exclude_id;
        }

        $result = (bool)Db::getInstance()->getValue($sql);
        self::setCache($cache_key, $result);
        
        return $result;
    }

    /**
     * Generate unique rewrite
     */
    public static function generateUniqueRewrite($base_rewrite, $type, $id_lang, $exclude_id = null)
    {
        $rewrite = self::sanitizeRewrite($base_rewrite);
        $counter = 1;
        
        while (self::rewriteExists($rewrite, $type, $id_lang, $exclude_id)) {
            $rewrite = self::sanitizeRewrite($base_rewrite) . '-' . $counter;
            $counter++;
            
            // Prevent infinite loop
            if ($counter > 100) {
                $rewrite = self::sanitizeRewrite($base_rewrite) . '-' . time();
                break;
            }
        }
        
        return $rewrite;
    }

    /**
     * Log debug message
     */
    public static function log($message, $level = 'info')
    {
        if (!Configuration::get('PRETTYURLS_DEBUG_MODE')) {
            return;
        }

        $log_file = _PS_LOG_DIR_ . 'prettyurls.log';
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
        
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Check version compatibility
     */
    public static function isVersionCompatible($min_version = '1.7.0.0', $max_version = '9.0.99')
    {
        return version_compare(_PS_VERSION_, $min_version, '>=') && 
               version_compare(_PS_VERSION_, $max_version, '<=');
    }

    /**
     * Get configuration value with default
     */
    public static function getConfig($key, $default = null)
    {
        $value = Configuration::get($key);
        return ($value !== false) ? $value : $default;
    }

    /**
     * Validate URL parameters
     */
    public static function validateUrlParams($params)
    {
        foreach ($params as $key => $value) {
            if (!is_string($value) && !is_numeric($value)) {
                return false;
            }
            
            // Basic XSS protection
            if (preg_match('/<script|javascript:|on\w+=/i', $value)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Get duplicate rewrites
     */
    public static function getDuplicateRewrites($type = 'product', $id_lang = null)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $cache_key = "duplicates_{$type}_{$id_lang}";
        
        $cached = self::getFromCache($cache_key);
        if ($cached !== false) {
            return $cached;
        }

        $table = '';
        $id_field = '';
        
        switch ($type) {
            case 'product':
                $table = _DB_PREFIX_ . 'product_lang';
                $id_field = 'id_product';
                break;
            case 'category':
                $table = _DB_PREFIX_ . 'category_lang';
                $id_field = 'id_category';
                break;
            case 'cms':
                $table = _DB_PREFIX_ . 'cms_lang';
                $id_field = 'id_cms';
                break;
            default:
                return [];
        }

        $sql = 'SELECT link_rewrite, COUNT(*) as count, GROUP_CONCAT(' . $id_field . ') as ids
                FROM ' . $table . ' 
                WHERE id_lang = ' . (int)$id_lang . ' 
                AND link_rewrite != ""
                GROUP BY link_rewrite 
                HAVING count > 1';

        $result = Db::getInstance()->executeS($sql);
        self::setCache($cache_key, $result ?: []);
        
        return $result ?: [];
    }

    /**
     * Fix duplicate rewrites
     */
    public static function fixDuplicateRewrites($type = 'product', $id_lang = null)
    {
        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $duplicates = self::getDuplicateRewrites($type, $id_lang);
        $fixed = 0;

        foreach ($duplicates as $duplicate) {
            $ids = explode(',', $duplicate['ids']);
            
            // Keep the first one, fix the rest
            for ($i = 1; $i < count($ids); $i++) {
                $new_rewrite = self::generateUniqueRewrite($duplicate['link_rewrite'], $type, $id_lang, $ids[$i]);
                
                $table = '';
                $id_field = '';
                
                switch ($type) {
                    case 'product':
                        $table = _DB_PREFIX_ . 'product_lang';
                        $id_field = 'id_product';
                        break;
                    case 'category':
                        $table = _DB_PREFIX_ . 'category_lang';
                        $id_field = 'id_category';
                        break;
                    case 'cms':
                        $table = _DB_PREFIX_ . 'cms_lang';
                        $id_field = 'id_cms';
                        break;
                }

                $sql = 'UPDATE ' . $table . ' 
                        SET link_rewrite = "' . pSQL($new_rewrite) . '" 
                        WHERE ' . $id_field . ' = ' . (int)$ids[$i] . ' 
                        AND id_lang = ' . (int)$id_lang;
                
                if (Db::getInstance()->execute($sql)) {
                    $fixed++;
                }
            }
        }

        // Clear cache after fixing
        self::clearCache();
        
        return $fixed;
    }
}
