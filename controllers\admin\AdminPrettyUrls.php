<?php
/**
 * Pretty URLs Pro - Admin Controller
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2025
 * @license   Academic Free License (AFL 3.0)
 * @version   2.0.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once dirname(__FILE__) . '/../../includes/Helper.php';

class AdminPrettyUrlsController extends ModuleAdminController
{
    public function __construct()
    {
        $this->bootstrap = true;
        $this->table = 'prettyurls';
        $this->className = 'PrettyUrls';
        
        parent::__construct();
        
        $this->meta_title = $this->module->l('Pretty URLs Management');
    }

    public function initContent()
    {
        $this->content = '';
        
        if (Tools::isSubmit('fixDuplicates')) {
            $this->content .= $this->processFixDuplicates();
        }
        
        $this->content .= $this->renderDuplicateChecker();
        $this->content .= $this->renderStatistics();
        
        $this->context->smarty->assign([
            'content' => $this->content,
            'url_post' => self::$currentIndex . '&token=' . $this->token,
            'show_page_header_toolbar' => $this->show_page_header_toolbar,
            'page_header_toolbar_title' => $this->page_header_toolbar_title,
            'page_header_toolbar_btn' => $this->page_header_toolbar_btn
        ]);
    }

    public function renderDuplicateChecker()
    {
        $output = '<div class="panel">';
        $output .= '<div class="panel-heading">';
        $output .= '<i class="icon-warning"></i> ' . $this->module->l('Duplicate URL Detection');
        $output .= '</div>';

        // Get duplicates for each type
        $product_duplicates = PrettyUrlsHelper::getDuplicateRewrites('product');
        $category_duplicates = PrettyUrlsHelper::getDuplicateRewrites('category');
        $cms_duplicates = PrettyUrlsHelper::getDuplicateRewrites('cms');

        $total_duplicates = count($product_duplicates) + count($category_duplicates) + count($cms_duplicates);

        if ($total_duplicates == 0) {
            $output .= '<div class="alert alert-success">';
            $output .= '<i class="icon-check"></i> ' . $this->module->l('No duplicate URLs found!');
            $output .= '</div>';
        } else {
            $output .= '<div class="alert alert-warning">';
            $output .= '<i class="icon-warning"></i> ' . sprintf($this->module->l('Found %d duplicate URL(s) that need attention.'), $total_duplicates);
            $output .= '</div>';

            // Fix duplicates button
            $output .= '<div class="panel-footer">';
            $output .= '<a href="' . self::$currentIndex . '&token=' . $this->token . '&fixDuplicates=1" class="btn btn-warning">';
            $output .= '<i class="icon-wrench"></i> ' . $this->module->l('Fix All Duplicates Automatically');
            $output .= '</a>';
            $output .= '</div>';

            // Show duplicate details
            if (!empty($product_duplicates)) {
                $output .= $this->renderDuplicateTable('Products', $product_duplicates);
            }

            if (!empty($category_duplicates)) {
                $output .= $this->renderDuplicateTable('Categories', $category_duplicates);
            }

            if (!empty($cms_duplicates)) {
                $output .= $this->renderDuplicateTable('CMS Pages', $cms_duplicates);
            }
        }

        $output .= '</div>';

        return $output;
    }

    private function renderDuplicateTable($title, $duplicates)
    {
        $output = '<h4>' . sprintf($this->module->l('%s URL Duplicates'), $title) . '</h4>';
        $output .= '<table class="table table-striped">';
        $output .= '<thead>';
        $output .= '<tr>';
        $output .= '<th>' . $this->module->l('URL Rewrite') . '</th>';
        $output .= '<th>' . $this->module->l('IDs') . '</th>';
        $output .= '<th>' . $this->module->l('Count') . '</th>';
        $output .= '</tr>';
        $output .= '</thead>';
        $output .= '<tbody>';
        
        foreach ($duplicates as $duplicate) {
            $output .= '<tr>';
            $output .= '<td><code>' . htmlspecialchars($duplicate['link_rewrite']) . '</code></td>';
            $output .= '<td>' . htmlspecialchars($duplicate['ids']) . '</td>';
            $output .= '<td><span class="badge badge-danger">' . (int)$duplicate['count'] . '</span></td>';
            $output .= '</tr>';
        }
        
        $output .= '</tbody>';
        $output .= '</table>';

        return $output;
    }

    public function renderStatistics()
    {
        $output = '<div class="panel">';
        $output .= '<div class="panel-heading">';
        $output .= '<i class="icon-bar-chart"></i> ' . $this->module->l('URL Statistics');
        $output .= '</div>';

        // Get statistics
        $stats = $this->getUrlStatistics();

        $output .= '<div class="row">';
        
        $output .= '<div class="col-md-3">';
        $output .= '<div class="panel panel-default">';
        $output .= '<div class="panel-body text-center">';
        $output .= '<h3 class="text-primary">' . (int)$stats['products'] . '</h3>';
        $output .= '<p>' . $this->module->l('Product URLs') . '</p>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '<div class="col-md-3">';
        $output .= '<div class="panel panel-default">';
        $output .= '<div class="panel-body text-center">';
        $output .= '<h3 class="text-success">' . (int)$stats['categories'] . '</h3>';
        $output .= '<p>' . $this->module->l('Category URLs') . '</p>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '<div class="col-md-3">';
        $output .= '<div class="panel panel-default">';
        $output .= '<div class="panel-body text-center">';
        $output .= '<h3 class="text-info">' . (int)$stats['cms'] . '</h3>';
        $output .= '<p>' . $this->module->l('CMS URLs') . '</p>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '<div class="col-md-3">';
        $output .= '<div class="panel panel-default">';
        $output .= '<div class="panel-body text-center">';
        $output .= '<h3 class="text-warning">' . (int)$stats['duplicates'] . '</h3>';
        $output .= '<p>' . $this->module->l('Duplicates') . '</p>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '</div>';
        $output .= '</div>';

        return $output;
    }

    private function getUrlStatistics()
    {
        $id_lang = Context::getContext()->language->id;
        $id_shop = Context::getContext()->shop->id;

        $stats = [];

        // Count products
        $sql = 'SELECT COUNT(*) FROM ' . _DB_PREFIX_ . 'product_lang 
                WHERE id_lang = ' . (int)$id_lang . ' 
                AND id_shop = ' . (int)$id_shop . ' 
                AND link_rewrite != ""';
        $stats['products'] = Db::getInstance()->getValue($sql);

        // Count categories
        $sql = 'SELECT COUNT(*) FROM ' . _DB_PREFIX_ . 'category_lang 
                WHERE id_lang = ' . (int)$id_lang . ' 
                AND id_shop = ' . (int)$id_shop . ' 
                AND link_rewrite != ""';
        $stats['categories'] = Db::getInstance()->getValue($sql);

        // Count CMS
        $sql = 'SELECT COUNT(*) FROM ' . _DB_PREFIX_ . 'cms_lang 
                WHERE id_lang = ' . (int)$id_lang . ' 
                AND id_shop = ' . (int)$id_shop . ' 
                AND link_rewrite != ""';
        $stats['cms'] = Db::getInstance()->getValue($sql);

        // Count duplicates
        $product_duplicates = PrettyUrlsHelper::getDuplicateRewrites('product');
        $category_duplicates = PrettyUrlsHelper::getDuplicateRewrites('category');
        $cms_duplicates = PrettyUrlsHelper::getDuplicateRewrites('cms');
        $stats['duplicates'] = count($product_duplicates) + count($category_duplicates) + count($cms_duplicates);

        return $stats;
    }

    public function processFixDuplicates()
    {
        $fixed_products = PrettyUrlsHelper::fixDuplicateRewrites('product');
        $fixed_categories = PrettyUrlsHelper::fixDuplicateRewrites('category');
        $fixed_cms = PrettyUrlsHelper::fixDuplicateRewrites('cms');

        $total_fixed = $fixed_products + $fixed_categories + $fixed_cms;

        if ($total_fixed > 0) {
            $output = '<div class="alert alert-success">';
            $output .= '<i class="icon-check"></i> ';
            $output .= sprintf($this->module->l('Successfully fixed %d duplicate URL(s)!'), $total_fixed);
            $output .= '<ul>';
            if ($fixed_products > 0) {
                $output .= '<li>' . sprintf($this->module->l('Products: %d'), $fixed_products) . '</li>';
            }
            if ($fixed_categories > 0) {
                $output .= '<li>' . sprintf($this->module->l('Categories: %d'), $fixed_categories) . '</li>';
            }
            if ($fixed_cms > 0) {
                $output .= '<li>' . sprintf($this->module->l('CMS Pages: %d'), $fixed_cms) . '</li>';
            }
            $output .= '</ul>';
            $output .= '</div>';
        } else {
            $output = '<div class="alert alert-info">';
            $output .= '<i class="icon-info"></i> ' . $this->module->l('No duplicates found to fix.');
            $output .= '</div>';
        }

        return $output;
    }
}
