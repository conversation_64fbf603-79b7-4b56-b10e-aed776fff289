<?php
/**
 * Pretty Urls Module
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2025
 * @license   Academic Free License (AFL 3.0)
 * @version   1.0.0
 */


class AdminPrettyUrlsController extends ModuleAdminController {

	public function __construct()
	{
		$this->bootstrap = true;
		$this->context = Context::getContext();
		parent::__construct();
	}

	public function renderList()
	{
		$product_urls = $this->getAllProductCollisions();
		$category_urls = $this->getAllCategoryCollisions();
		$compare_urls = $this->getAllCompareCollisions();
		$langs = Language::getLanguages();
		$langs = count($langs);
		
		$this->context->smarty->assign(array(
			'product_coll' => $product_urls,
			'category_coll' => $category_urls,
			'compare_coll' => $compare_urls,
			'langs_active' => (int)$langs
		));

		parent::renderList();
		return $this->context->smarty->fetch(dirname(__FILE__).'/../../views/templates/admin/pretty_urls/helpers/form/form.tpl');
	}

	private function getAllProductCollisions()
	{
		$langs = Language::getLanguages();
		$langs = count($langs);
		return Db::getInstance()->executeS('
		SELECT DISTINCT `link_rewrite`, GROUP_CONCAT(DISTINCT `id_product`) as id_product, `name`, count(`link_rewrite`) as times
		FROM `'._DB_PREFIX_.'product_lang`
		GROUP BY `link_rewrite`
		HAVING COUNT(`link_rewrite`) > '.(int)$langs);
	}

	private function getAllCategoryCollisions()
	{
		$langs = Language::getLanguages();
		$langs = count($langs);
		return Db::getInstance()->executeS('
		SELECT DISTINCT `link_rewrite`, GROUP_CONCAT(DISTINCT `id_category`) as id_category, `name`, count(`link_rewrite`) as times
		FROM `'._DB_PREFIX_.'category_lang`
		GROUP BY `link_rewrite`
		HAVING COUNT(`link_rewrite`) > '.(int)$langs);
	}

	private function getAllCompareCollisions()
	{
		return Db::getInstance()->executeS('
		SELECT DISTINCT p.`link_rewrite`, p.`id_product`, c.`link_rewrite`, c.`id_category`
		FROM `'._DB_PREFIX_.'product_lang` p, `'._DB_PREFIX_.'category_lang` c
		WHERE p.`link_rewrite` = c.`link_rewrite`');
	}
}