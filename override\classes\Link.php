<?php
/**
 * Pretty URLs Pro - Link Override
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2025
 * @license   Academic Free License (AFL 3.0)
 * @version   2.0.0
 */

class Link extends LinkCore
{
    private static $cache = [];
    
    /**
     * Get category link without ID
     */
    public function getCategoryLink($category, $alias = null, $id_lang = null, $selected_filters = null, $id_shop = null, $relative_protocol = false)
    {
        if (!Configuration::get('PRETTYURLS_ENABLE_CATEGORIES')) {
            return parent::getCategoryLink($category, $alias, $id_lang, $selected_filters, $id_shop, $relative_protocol);
        }

        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $url = $this->getBaseLink($id_shop, null, $relative_protocol) . $this->getLangLink($id_lang, null, $id_shop);

        if (!is_object($category)) {
            $category = new Category($category, $id_lang);
        }

        $params = [];
        $params['rewrite'] = (!$alias) ? $category->link_rewrite : $alias;
        
        if (Configuration::get('PRETTYURLS_FORCE_LOWERCASE')) {
            $params['rewrite'] = Tools::strtolower($params['rewrite']);
        }

        // Handle selected filters for layered navigation
        $selected_filters = is_null($selected_filters) ? '' : $selected_filters;
        if (!empty($selected_filters)) {
            $rule = 'layered_rule';
            $params['selected_filters'] = $selected_filters;
        } else {
            $rule = 'category_rule';
        }

        return $url . Dispatcher::getInstance()->createUrl($rule, $id_lang, $params, $this->allow, '', $id_shop);
    }

    /**
     * Get product link without ID
     */
    public function getProductLink($product, $alias = null, $category = null, $ean13 = null, $id_lang = null, $id_shop = null, $id_product_attribute = 0, $force_routes = false, $relative_protocol = false, $with_id_in_anchor = false, $extraParams = [])
    {
        if (!Configuration::get('PRETTYURLS_ENABLE_PRODUCTS')) {
            return parent::getProductLink($product, $alias, $category, $ean13, $id_lang, $id_shop, $id_product_attribute, $force_routes, $relative_protocol, $with_id_in_anchor, $extraParams);
        }

        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $url = $this->getBaseLink($id_shop, null, $relative_protocol) . $this->getLangLink($id_lang, null, $id_shop);

        if (!is_object($product)) {
            $product = new Product($product, false, $id_lang);
        }

        $params = [];
        $params['rewrite'] = (!$alias) ? $product->link_rewrite : $alias;
        
        if (Configuration::get('PRETTYURLS_FORCE_LOWERCASE')) {
            $params['rewrite'] = Tools::strtolower($params['rewrite']);
        }

        // Add category path if available
        if ($category && is_object($category)) {
            $params['categories'] = $this->getCategoryPath($category);
        }

        // Add product attribute if specified
        if ($id_product_attribute) {
            $params['id_product_attribute'] = $id_product_attribute;
        }

        return $url . Dispatcher::getInstance()->createUrl('product_rule', $id_lang, $params, $this->allow, '', $id_shop);
    }

    /**
     * Get manufacturer link without ID
     */
    public function getManufacturerLink($manufacturer, $alias = null, $id_lang = null, $id_shop = null, $relative_protocol = false)
    {
        if (!Configuration::get('PRETTYURLS_ENABLE_MANUFACTURERS')) {
            return parent::getManufacturerLink($manufacturer, $alias, $id_lang, $id_shop, $relative_protocol);
        }

        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $url = $this->getBaseLink($id_shop, null, $relative_protocol) . $this->getLangLink($id_lang, null, $id_shop);

        if (!is_object($manufacturer)) {
            $manufacturer = new Manufacturer($manufacturer, $id_lang);
        }

        $params = [];
        $params['rewrite'] = (!$alias) ? $manufacturer->link_rewrite : $alias;
        
        if (Configuration::get('PRETTYURLS_FORCE_LOWERCASE')) {
            $params['rewrite'] = Tools::strtolower($params['rewrite']);
        }

        return $url . Dispatcher::getInstance()->createUrl('manufacturer_rule', $id_lang, $params, $this->allow, '', $id_shop);
    }

    /**
     * Get supplier link without ID
     */
    public function getSupplierLink($supplier, $alias = null, $id_lang = null, $id_shop = null, $relative_protocol = false)
    {
        if (!Configuration::get('PRETTYURLS_ENABLE_SUPPLIERS')) {
            return parent::getSupplierLink($supplier, $alias, $id_lang, $id_shop, $relative_protocol);
        }

        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $url = $this->getBaseLink($id_shop, null, $relative_protocol) . $this->getLangLink($id_lang, null, $id_shop);

        if (!is_object($supplier)) {
            $supplier = new Supplier($supplier, $id_lang);
        }

        $params = [];
        $params['rewrite'] = (!$alias) ? $supplier->link_rewrite : $alias;
        
        if (Configuration::get('PRETTYURLS_FORCE_LOWERCASE')) {
            $params['rewrite'] = Tools::strtolower($params['rewrite']);
        }

        return $url . Dispatcher::getInstance()->createUrl('supplier_rule', $id_lang, $params, $this->allow, '', $id_shop);
    }

    /**
     * Get CMS link without ID
     */
    public function getCMSLink($cms, $alias = null, $ssl = null, $id_lang = null, $id_shop = null, $relative_protocol = false)
    {
        if (!Configuration::get('PRETTYURLS_ENABLE_CMS')) {
            return parent::getCMSLink($cms, $alias, $ssl, $id_lang, $id_shop, $relative_protocol);
        }

        if (!$id_lang) {
            $id_lang = Context::getContext()->language->id;
        }

        $url = $this->getBaseLink($id_shop, $ssl, $relative_protocol) . $this->getLangLink($id_lang, null, $id_shop);

        if (!is_object($cms)) {
            $cms = new CMS($cms, $id_lang);
        }

        $params = [];
        $params['rewrite'] = (!$alias) ? $cms->link_rewrite : $alias;
        
        if (Configuration::get('PRETTYURLS_FORCE_LOWERCASE')) {
            $params['rewrite'] = Tools::strtolower($params['rewrite']);
        }

        return $url . Dispatcher::getInstance()->createUrl('cms_rule', $id_lang, $params, $this->allow, '', $id_shop);
    }

    /**
     * Get category path for breadcrumb
     */
    private function getCategoryPath($category)
    {
        $cache_key = 'category_path_' . $category->id;
        
        if (isset(self::$cache[$cache_key])) {
            return self::$cache[$cache_key];
        }

        $path = [];
        $current_category = $category;
        
        while ($current_category && $current_category->id != Configuration::get('PS_HOME_CATEGORY')) {
            if ($current_category->link_rewrite) {
                $rewrite = $current_category->link_rewrite;
                if (Configuration::get('PRETTYURLS_FORCE_LOWERCASE')) {
                    $rewrite = Tools::strtolower($rewrite);
                }
                array_unshift($path, $rewrite);
            }
            
            if ($current_category->id_parent) {
                $current_category = new Category($current_category->id_parent, Context::getContext()->language->id);
            } else {
                break;
            }
        }

        $result = implode('/', $path);
        self::$cache[$cache_key] = $result;
        
        return $result;
    }

    /**
     * Clear internal cache
     */
    public static function clearCache()
    {
        self::$cache = [];
    }
}
