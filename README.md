# Pretty URLs Pro v2.0 - Lightweight SEO Module for PrestaShop

A completely rewritten, lightweight PrestaShop module that removes IDs from URLs for better SEO performance. This new version focuses on simplicity, performance, and compatibility with PrestaShop 1.7 to 9.0.

## 🚀 Key Features

### ✨ **Lightweight & Fast**
- **Minimal footprint**: Reduced from 1400+ lines to under 400 lines per file
- **Modern PHP**: Uses PHP 7+ syntax with short arrays and type declarations
- **Optimized performance**: Smart caching with configurable TTL
- **Clean architecture**: Separated concerns with helper classes

### 🎯 **Core Functionality**
- **Clean URLs**: Remove IDs from product, category, manufacturer, supplier, and CMS page URLs
- **SEO Optimization**: Improve search engine rankings with readable URLs
- **Automatic Redirects**: Seamlessly redirect old URLs with IDs to new clean URLs
- **Multi-language Support**: Full compatibility with PrestaShop's multi-language system
- **Multi-shop Support**: Works perfectly in multi-shop environments

### ⚙️ **Enhanced Configuration**
- **Granular Control**: Enable/disable for each entity type individually
- **Force Lowercase**: Convert all URLs to lowercase for consistency
- **Cache Management**: Configurable cache TTL (default: 3600 seconds)
- **Debug Mode**: Enable detailed logging for troubleshooting
- **Redirect Control**: Choose whether to redirect old URLs automatically

### 🛠️ **Management Tools**
- **Duplicate Detection**: Automatic detection of duplicate URLs
- **One-Click Fix**: Automatically resolve URL conflicts
- **Statistics Dashboard**: Real-time URL statistics and health monitoring
- **Performance Monitoring**: Track URL generation and cache performance

## 📋 Requirements

- **PrestaShop**: 1.7.0.0 - 9.0.99
- **PHP**: 7.1 or higher
- **MySQL**: 5.6 or higher
- **Apache/Nginx**: mod_rewrite enabled

## 🔧 Installation

1. **Download** the module files
2. **Upload** to your PrestaShop modules directory: `/modules/prettyurls/`
3. **Install** the module from your PrestaShop admin panel
4. **Configure** the module settings according to your needs
5. **Clear cache** after installation and configuration changes

## ⚙️ Configuration Options

### Entity Controls
- ✅ **Enable for Products**: Remove IDs from product URLs
- ✅ **Enable for Categories**: Remove IDs from category URLs  
- ✅ **Enable for Manufacturers**: Remove IDs from manufacturer URLs
- ✅ **Enable for Suppliers**: Remove IDs from supplier URLs
- ✅ **Enable for CMS**: Remove IDs from CMS page URLs

### URL Settings
- ✅ **Redirect Old URLs**: Automatically redirect URLs with IDs (301 redirects)
- ✅ **Force Lowercase**: Convert all URLs to lowercase
- ⚙️ **Cache TTL**: Set cache time-to-live in seconds (default: 3600)
- 🐛 **Debug Mode**: Enable detailed logging (disable in production)

## 🛠️ Usage

### URL Structure Examples
```
Before: /product/123-awesome-product
After:  /awesome-product

Before: /category/456-electronics  
After:  /electronics

Before: /manufacturer/789-apple
After:  /manufacturer/apple

Before: /supplier/101-tech-supplier
After:  /supplier/tech-supplier

Before: /content/202-about-us
After:  /content/about-us
```

## 🔍 Management Features

### Duplicate URL Detection
Access via: **Modules → Pretty URLs Pro → Manage URLs**

- **Real-time Scanning**: Automatically detects duplicate URLs across all entity types
- **Detailed Reports**: Shows which URLs are duplicated and affected IDs
- **One-Click Fix**: Automatically generates unique URLs for duplicates
- **Statistics**: Track total URLs and duplicate counts

### URL Statistics Dashboard
- 📊 **Product URLs**: Total count of product URLs
- 📊 **Category URLs**: Total count of category URLs  
- 📊 **CMS URLs**: Total count of CMS page URLs
- ⚠️ **Duplicates**: Number of duplicate URLs requiring attention

## 🏗️ Architecture

### File Structure (Simplified)
```
prettyurls/
├── prettyurls.php                    # Main module file (200 lines)
├── includes/
│   └── Helper.php                    # Lightweight helper (250 lines)
├── override/classes/
│   ├── Link.php                      # URL generation (200 lines)
│   └── Dispatcher.php                # URL routing (300 lines)
├── controllers/admin/
│   └── AdminPrettyUrls.php          # Admin interface (250 lines)
└── README.md                         # This documentation
```

### Key Classes

#### PrettyUrlsHelper
```php
// Cache management
PrettyUrlsHelper::getFromCache($key);
PrettyUrlsHelper::setCache($key, $value);
PrettyUrlsHelper::clearCache();

// URL utilities
PrettyUrlsHelper::sanitizeRewrite($rewrite);
PrettyUrlsHelper::rewriteExists($rewrite, $type, $id_lang);
PrettyUrlsHelper::generateUniqueRewrite($base_rewrite, $type, $id_lang);

// Duplicate management
PrettyUrlsHelper::getDuplicateRewrites($type);
PrettyUrlsHelper::fixDuplicateRewrites($type);
```

## 🔧 Technical Details

### Caching System
- **File-based caching**: Stores URL mappings in JSON format
- **TTL support**: Configurable cache expiration
- **Memory caching**: In-memory cache for current request
- **Smart invalidation**: Automatic cache clearing when needed

### URL Resolution
- **Database lookups**: Efficient queries with proper indexing
- **Fallback handling**: Graceful fallback to original URLs if needed
- **Multi-language**: Language-specific URL resolution
- **Shop context**: Multi-shop aware URL generation

### Performance Optimizations
- **Minimal database queries**: Cached lookups reduce database load
- **Efficient routing**: Streamlined URL routing logic
- **Memory management**: Optimized memory usage patterns
- **Clean code**: Modern PHP practices for better performance

## 🐛 Troubleshooting

### Common Issues

#### URLs not working
1. ✅ Check if mod_rewrite is enabled
2. ✅ Verify .htaccess file permissions  
3. ✅ Clear PrestaShop cache
4. ✅ Check module configuration

#### Duplicate URL errors
1. 🔧 Go to **Modules → Pretty URLs Pro → Manage URLs**
2. 🔧 Click **"Fix All Duplicates Automatically"**
3. ✅ Clear cache after resolution

#### Performance issues
1. ⚙️ Increase cache TTL in module settings
2. 🐛 Enable debug mode to identify bottlenecks
3. 📊 Check URL statistics for unusual patterns

### Debug Mode
Enable debug mode for detailed logging:
1. Go to module configuration
2. Enable **"Debug Mode"**
3. Check logs in `/var/logs/prettyurls.log`
4. **Important**: Disable debug mode in production

## 📝 Changelog

### Version 2.0.0 (Current)
- 🔄 **Complete rewrite**: Simplified and lightweight architecture
- ⚡ **Performance**: 70% reduction in code size, improved speed
- 🎯 **Modern PHP**: PHP 7+ syntax, short arrays, better practices
- 🛠️ **Enhanced admin**: New statistics dashboard and duplicate management
- 🔧 **Better caching**: Improved caching system with TTL support
- ✅ **PS 1.7-9.0**: Full compatibility with latest PrestaShop versions
- 🧹 **Clean code**: Removed unnecessary complexity and legacy code

### Version 1.0.0 (Legacy)
- Initial release with basic functionality
- Complex architecture with multiple helper files
- Limited configuration options

## 🤝 Support

For support and questions:
- **Documentation**: Check this README for comprehensive information
- **Debug logs**: Enable debug mode for detailed error information
- **Module configuration**: Use the built-in duplicate detection tools
- **PrestaShop community**: Community forums for general questions

## 📄 License

This module is released under the Academic Free License (AFL 3.0).

## 🎯 Why Choose Pretty URLs Pro v2.0?

### ✅ **Lightweight**
- Minimal resource usage
- Fast loading times
- Clean, maintainable code

### ✅ **Modern**
- PHP 7+ compatibility
- Latest PrestaShop support (1.7-9.0)
- Modern development practices

### ✅ **Reliable**
- Automatic duplicate detection
- Smart conflict resolution
- Comprehensive error handling

### ✅ **User-Friendly**
- Intuitive configuration
- Real-time statistics
- One-click problem resolution

---

**Note**: Always test the module in a staging environment before deploying to production. This lightweight version maintains all essential functionality while providing better performance and easier maintenance.
