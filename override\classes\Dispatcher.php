<?php
/**
 * Pretty URLs Pro - Dispatcher Override
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2025
 * @license   Academic Free License (AFL 3.0)
 * @version   2.0.0
 */

class Dispatcher extends DispatcherCore
{
    private static $url_cache = [];
    
    public $default_routes = [
        'category_rule' => [
            'controller' => 'category',
            'rule' => '{rewrite}',
            'keywords' => [
                'id' => ['regexp' => '[0-9]+'],
                'rewrite' => ['regexp' => '[_a-zA-Z0-9-\pL]*', 'param' => 'category_rewrite'],
                'meta_keywords' => ['regexp' => '[_a-zA-Z0-9-\pL]*'],
                'meta_title' => ['regexp' => '[_a-zA-Z0-9-\pL]*'],
            ],
        ],
        'product_rule' => [
            'controller' => 'product',
            'rule' => '{categories:/}{rewrite}',
            'keywords' => [
                'id' => ['regexp' => '[0-9]+'],
                'rewrite' => ['regexp' => '[_a-zA-Z0-9-\pL]*', 'param' => 'product_rewrite'],
                'id_product_attribute' => ['regexp' => '[0-9]+'],
                'categories' => ['regexp' => '[/_a-zA-Z0-9-\pL]*'],
                'reference' => ['regexp' => '[_a-zA-Z0-9-\pL]*'],
                'meta_keywords' => ['regexp' => '[_a-zA-Z0-9-\pL]*'],
                'meta_title' => ['regexp' => '[_a-zA-Z0-9-\pL]*'],
            ],
        ],
        'manufacturer_rule' => [
            'controller' => 'manufacturer',
            'rule' => 'manufacturer/{rewrite}',
            'keywords' => [
                'id' => ['regexp' => '[0-9]+'],
                'rewrite' => ['regexp' => '[_a-zA-Z0-9-\pL]*', 'param' => 'manufacturer_rewrite'],
                'meta_keywords' => ['regexp' => '[_a-zA-Z0-9-\pL]*'],
                'meta_title' => ['regexp' => '[_a-zA-Z0-9-\pL]*'],
            ],
        ],
        'supplier_rule' => [
            'controller' => 'supplier',
            'rule' => 'supplier/{rewrite}',
            'keywords' => [
                'id' => ['regexp' => '[0-9]+'],
                'rewrite' => ['regexp' => '[_a-zA-Z0-9-\pL]*', 'param' => 'supplier_rewrite'],
                'meta_keywords' => ['regexp' => '[_a-zA-Z0-9-\pL]*'],
                'meta_title' => ['regexp' => '[_a-zA-Z0-9-\pL]*'],
            ],
        ],
        'cms_rule' => [
            'controller' => 'cms',
            'rule' => 'content/{rewrite}',
            'keywords' => [
                'id' => ['regexp' => '[0-9]+'],
                'rewrite' => ['regexp' => '[_a-zA-Z0-9-\pL]*', 'param' => 'cms_rewrite'],
                'meta_keywords' => ['regexp' => '[_a-zA-Z0-9-\pL]*'],
                'meta_title' => ['regexp' => '[_a-zA-Z0-9-\pL]*'],
            ],
        ],
        'layered_rule' => [
            'controller' => 'category',
            'rule' => '{rewrite}/filter{selected_filters}',
            'keywords' => [
                'id' => ['regexp' => '[0-9]+'],
                'selected_filters' => ['regexp' => '.*', 'param' => 'selected_filters'],
                'rewrite' => ['regexp' => '[_a-zA-Z0-9-\pL]*', 'param' => 'category_rewrite'],
                'meta_keywords' => ['regexp' => '[_a-zA-Z0-9-\pL]*'],
                'meta_title' => ['regexp' => '[_a-zA-Z0-9-\pL]*'],
            ],
        ],
        'module' => [
            'controller' => null,
            'rule' => 'module/{module}{/:controller}',
            'keywords' => [
                'module' => ['regexp' => '[_a-zA-Z0-9_-]+', 'param' => 'module'],
                'controller' => ['regexp' => '[_a-zA-Z0-9_-]+', 'param' => 'controller'],
            ],
            'params' => [
                'fc' => 'module',
            ],
        ],
    ];

    public function getController($id_shop = null)
    {
        if (defined('_PS_ADMIN_DIR_')) {
            $_GET['controllerUri'] = Tools::getValue('controller');
        }
        
        if ($this->controller) {
            $_GET['controller'] = $this->controller;
            return $this->controller;
        }

        if (isset(Context::getContext()->shop) && $id_shop === null) {
            $id_shop = (int)Context::getContext()->shop->id;
        }

        $controller = Tools::getValue('controller');

        if (isset($controller) && is_string($controller) && preg_match('/^([0-9a-z_-]+)\?(.*)=(.*)$/Ui', $controller, $m)) {
            $controller = $m[1];
            if (isset($_GET['controller'])) {
                $_GET[$m[2]] = $m[3];
            } else if (isset($_POST['controller'])) {
                $_POST[$m[2]] = $m[3];
            }
        }

        if (!Validate::isControllerName($controller)) {
            $controller = false;
        }

        // Use routes for URL rewriting
        if ($this->use_routes && !$controller && !defined('_PS_ADMIN_DIR_')) {
            if (!$this->request_uri) {
                return Tools::strtolower($this->controller_not_found);
            }

            $controller = $this->controller_not_found;
            $test_request_uri = preg_replace('/(=http:\/\/)/', '=', $this->request_uri);

            // Skip static files
            if (!preg_match('/\.(gif|jpe?g|png|css|js|ico)$/i', parse_url($test_request_uri, PHP_URL_PATH))) {
                // Add empty route as last route
                if ($this->empty_route) {
                    $this->addRoute($this->empty_route['routeID'], $this->empty_route['rule'], $this->empty_route['controller'], Context::getContext()->language->id, [], [], $id_shop);
                }

                list($uri) = explode('?', $this->request_uri);

                if (isset($this->routes[$id_shop][Context::getContext()->language->id])) {
                    foreach ($this->routes[$id_shop][Context::getContext()->language->id] as $route) {
                        if (preg_match($route['regexp'], $uri, $m)) {
                            // Route found! Fill $_GET with parameters
                            foreach ($m as $k => $v) {
                                if (!is_numeric($k)) {
                                    $_GET[$k] = $v;
                                }
                            }
                            
                            $controller = $route['controller'] ? $route['controller'] : $_GET['controller'];
                            
                            if (!empty($route['params'])) {
                                foreach ($route['params'] as $k => $v) {
                                    $_GET[$k] = $v;
                                }
                            }

                            // Handle module friendly URLs
                            if (preg_match('#module-([a-z0-9_-]+)-([a-z0-9_]+)$#i', $controller, $m)) {
                                $_GET['module'] = $m[1];
                                $_GET['fc'] = 'module';
                                $controller = $m[2];
                            }
                            
                            if (isset($_GET['fc']) && $_GET['fc'] == 'module') {
                                $this->front_controller = self::FC_MODULE;
                            }
                            
                            break;
                        }
                    }
                }

                // Try to resolve controller from URL
                $controller = $this->resolveController($uri, $controller);
            }

            // Handle index page
            $req_uri = explode('/', $this->request_uri);
            if (preg_match('/\?/', $req_uri[1])) {
                $req_uri_qmark = explode('?', $req_uri[1]);
                $req_uri[1] = $req_uri_qmark[0];
            }
            
            if ($controller == 'index' || preg_match('/^\/index.php(?:\?.*)?$/', $this->request_uri) || $req_uri[1] == '') {
                $controller = version_compare(_PS_VERSION_, '1.6.0', '>=') ? $this->useDefaultController() : $this->default_controller;
            }
        }

        // Handle 404 pages
        if (!defined('_PS_ADMIN_DIR_')) {
            if ($controller == '404' || $controller == 404 || $controller == 'page-not-found' || $controller == 'pagenotfound' || (isset($_GET['controller']) && $_GET['controller'] == 'pagenotfound')) {
                $controller = 'pagenotfound';
            }
        }

        return Tools::strtolower($controller);
    }

    /**
     * Resolve controller from URL
     */
    private function resolveController($uri, $default_controller)
    {
        $uri = trim($uri, '/');
        
        if (empty($uri)) {
            return $default_controller;
        }

        // Check for product
        if (Configuration::get('PRETTYURLS_ENABLE_PRODUCTS')) {
            $product_id = $this->getProductIdByRewrite($uri);
            if ($product_id) {
                $_POST['id_product'] = $product_id;
                return 'product';
            }
        }

        // Check for category
        if (Configuration::get('PRETTYURLS_ENABLE_CATEGORIES')) {
            $category_id = $this->getCategoryIdByRewrite($uri);
            if ($category_id) {
                $_POST['id_category'] = $category_id;
                return 'category';
            }
        }

        // Check for manufacturer
        if (Configuration::get('PRETTYURLS_ENABLE_MANUFACTURERS') && strpos($uri, 'manufacturer/') === 0) {
            $rewrite = str_replace('manufacturer/', '', $uri);
            $manufacturer_id = $this->getManufacturerIdByRewrite($rewrite);
            if ($manufacturer_id) {
                $_POST['id_manufacturer'] = $manufacturer_id;
                return 'manufacturer';
            }
        }

        // Check for supplier
        if (Configuration::get('PRETTYURLS_ENABLE_SUPPLIERS') && strpos($uri, 'supplier/') === 0) {
            $rewrite = str_replace('supplier/', '', $uri);
            $supplier_id = $this->getSupplierIdByRewrite($rewrite);
            if ($supplier_id) {
                $_POST['id_supplier'] = $supplier_id;
                return 'supplier';
            }
        }

        // Check for CMS
        if (Configuration::get('PRETTYURLS_ENABLE_CMS') && strpos($uri, 'content/') === 0) {
            $rewrite = str_replace('content/', '', $uri);
            $cms_id = $this->getCmsIdByRewrite($rewrite);
            if ($cms_id) {
                $_POST['id_cms'] = $cms_id;
                return 'cms';
            }
        }

        return $default_controller;
    }

    /**
     * Get product ID by rewrite
     */
    private function getProductIdByRewrite($rewrite)
    {
        $cache_key = 'product_' . $rewrite;
        
        if (isset(self::$url_cache[$cache_key])) {
            return self::$url_cache[$cache_key];
        }

        // Handle category path in product URL
        $parts = explode('/', $rewrite);
        $product_rewrite = end($parts);

        $sql = 'SELECT id_product FROM ' . _DB_PREFIX_ . 'product_lang 
                WHERE link_rewrite = "' . pSQL($product_rewrite) . '" 
                AND id_lang = ' . (int)Context::getContext()->language->id . ' 
                AND id_shop = ' . (int)Context::getContext()->shop->id;
        
        $result = Db::getInstance()->getValue($sql);
        self::$url_cache[$cache_key] = $result;
        
        return $result;
    }

    /**
     * Get category ID by rewrite
     */
    private function getCategoryIdByRewrite($rewrite)
    {
        $cache_key = 'category_' . $rewrite;
        
        if (isset(self::$url_cache[$cache_key])) {
            return self::$url_cache[$cache_key];
        }

        $sql = 'SELECT id_category FROM ' . _DB_PREFIX_ . 'category_lang 
                WHERE link_rewrite = "' . pSQL($rewrite) . '" 
                AND id_lang = ' . (int)Context::getContext()->language->id . ' 
                AND id_shop = ' . (int)Context::getContext()->shop->id;
        
        $result = Db::getInstance()->getValue($sql);
        self::$url_cache[$cache_key] = $result;

        return $result;
    }

    /**
     * Get manufacturer ID by rewrite
     */
    private function getManufacturerIdByRewrite($rewrite)
    {
        $cache_key = 'manufacturer_' . $rewrite;

        if (isset(self::$url_cache[$cache_key])) {
            return self::$url_cache[$cache_key];
        }

        $sql = 'SELECT id_manufacturer FROM ' . _DB_PREFIX_ . 'manufacturer
                WHERE link_rewrite = "' . pSQL($rewrite) . '"';

        $result = Db::getInstance()->getValue($sql);
        self::$url_cache[$cache_key] = $result;

        return $result;
    }

    /**
     * Get supplier ID by rewrite
     */
    private function getSupplierIdByRewrite($rewrite)
    {
        $cache_key = 'supplier_' . $rewrite;

        if (isset(self::$url_cache[$cache_key])) {
            return self::$url_cache[$cache_key];
        }

        $sql = 'SELECT id_supplier FROM ' . _DB_PREFIX_ . 'supplier
                WHERE link_rewrite = "' . pSQL($rewrite) . '"';

        $result = Db::getInstance()->getValue($sql);
        self::$url_cache[$cache_key] = $result;

        return $result;
    }

    /**
     * Get CMS ID by rewrite
     */
    private function getCmsIdByRewrite($rewrite)
    {
        $cache_key = 'cms_' . $rewrite;

        if (isset(self::$url_cache[$cache_key])) {
            return self::$url_cache[$cache_key];
        }

        $sql = 'SELECT id_cms FROM ' . _DB_PREFIX_ . 'cms_lang
                WHERE link_rewrite = "' . pSQL($rewrite) . '"
                AND id_lang = ' . (int)Context::getContext()->language->id . '
                AND id_shop = ' . (int)Context::getContext()->shop->id;

        $result = Db::getInstance()->getValue($sql);
        self::$url_cache[$cache_key] = $result;

        return $result;
    }
}
